<?php
require_once '../config/database.php';
session_start();
requireRole(['officer']);

$record = null;
$message = '';
$error_message = '';

// Get record ID
if (!isset($_GET['id'])) {
    header('Location: manage_records.php');
    exit();
}

$record_id = (int)$_GET['id'];

// Load existing record
try {
    $pdo = getDBConnection();
    $stmt = $pdo->prepare("SELECT * FROM criminal_records WHERE record_id = ? AND created_by = ?");
    $stmt->execute([$record_id, $_SESSION['user_id']]);
    $record = $stmt->fetch();
    
    if (!$record) {
        $error_message = "Record not found or you don't have permission to edit it.";
    }
} catch (PDOException $e) {
    $error_message = "Failed to load record.";
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && $record) {
    $suspect_name = sanitizeInput($_POST['suspect_name']);
    $suspect_id_number = sanitizeInput($_POST['suspect_id_number']);
    $crime_type = sanitizeInput($_POST['crime_type']);
    $crime_description = sanitizeInput($_POST['crime_description']);
    $incident_date = $_POST['incident_date'];
    $incident_location = sanitizeInput($_POST['incident_location']);
    $evidence = sanitizeInput($_POST['evidence']);
    $witness_info = sanitizeInput($_POST['witness_info']);
    $witness_statements = sanitizeInput($_POST['witness_statements']);
    $case_status = $_POST['case_status'];
    $court_date = $_POST['court_date'] ?: null;
    $verdict = sanitizeInput($_POST['verdict']);
    
    try {
        $pdo = getDBConnection();
        
        // Handle file uploads
        $mugshot_filename = $record['suspect_mugshot'];
        $evidence_photos = json_decode($record['evidence_photos'] ?: '[]', true);
        
        // Upload new mugshot if provided
        if (isset($_FILES['suspect_mugshot']) && $_FILES['suspect_mugshot']['error'] == UPLOAD_ERR_OK) {
            $upload_result = uploadFile($_FILES['suspect_mugshot'], '../uploads/mugshots', ['jpg', 'jpeg', 'png']);
            if ($upload_result['success']) {
                // Delete old mugshot if exists
                if ($mugshot_filename && file_exists('../uploads/mugshots/' . $mugshot_filename)) {
                    unlink('../uploads/mugshots/' . $mugshot_filename);
                }
                $mugshot_filename = $upload_result['filename'];
            }
        }
        
        // Upload new evidence photos if provided
        if (isset($_FILES['evidence_photos']) && is_array($_FILES['evidence_photos']['name'])) {
            for ($i = 0; $i < count($_FILES['evidence_photos']['name']); $i++) {
                if ($_FILES['evidence_photos']['error'][$i] == UPLOAD_ERR_OK) {
                    $file = [
                        'name' => $_FILES['evidence_photos']['name'][$i],
                        'tmp_name' => $_FILES['evidence_photos']['tmp_name'][$i],
                        'size' => $_FILES['evidence_photos']['size'][$i],
                        'error' => $_FILES['evidence_photos']['error'][$i]
                    ];
                    $upload_result = uploadFile($file, '../uploads/evidence', ['jpg', 'jpeg', 'png', 'pdf']);
                    if ($upload_result['success']) {
                        $evidence_photos[] = $upload_result['filename'];
                    }
                }
            }
        }
        
        // Update record
        $stmt = $pdo->prepare("
            UPDATE criminal_records SET 
                suspect_name = ?, suspect_id_number = ?, suspect_mugshot = ?, 
                crime_type = ?, crime_description = ?, incident_date = ?, incident_location = ?,
                case_status = ?, evidence = ?, evidence_photos = ?, witness_info = ?, witness_statements = ?,
                court_date = ?, verdict = ?, updated_at = CURRENT_TIMESTAMP
            WHERE record_id = ? AND created_by = ?
        ");
        
        $stmt->execute([
            $suspect_name, $suspect_id_number, $mugshot_filename, $crime_type, $crime_description,
            $incident_date, $incident_location, $case_status, $evidence, json_encode($evidence_photos),
            $witness_info, $witness_statements, $court_date, $verdict, $record_id, $_SESSION['user_id']
        ]);
        
        // Add case update
        $stmt = $pdo->prepare("
            INSERT INTO case_updates (case_number, update_type, update_description, updated_by) 
            VALUES (?, 'general_update', 'Record updated by officer', ?)
        ");
        $stmt->execute([$record['case_number'], $_SESSION['user_id']]);
        
        $message = "Criminal record updated successfully!";
        
        // Reload record
        $stmt = $pdo->prepare("SELECT * FROM criminal_records WHERE record_id = ?");
        $stmt->execute([$record_id]);
        $record = $stmt->fetch();
        
    } catch (PDOException $e) {
        $error_message = "Failed to update record. Please try again.";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <title>Edit Criminal Record - CRMS Officer</title>
    <style>
        .form-container {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border: 2px solid #dc2626;
            border-radius: 8px;
            padding: 30px;
        }
        .form-control {
            width: 100%;
            border: 2px solid #dc2626;
            border-radius: 5px;
            background: transparent;
            color: white;
            padding: 10px 15px;
        }
        .form-control:focus {
            outline: none;
            border-color: #ef4444;
            box-shadow: 0 0 10px rgba(220, 38, 38, 0.3);
        }
        textarea.form-control {
            min-height: 100px;
            resize: vertical;
        }
        option{
            color:black;
        }
    </style>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - Edit Criminal Record</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="manage_records.php" class="text-white hover:text-gray-300">My Cases</a>
                <a href="dashboard.php" class="text-white hover:text-gray-300">Dashboard</a>
                <span class="text-white">Officer <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8">
        <?php if (!empty($error_message)): ?>
            <div class="bg-red-600 text-white p-4 rounded mb-6">
                <?php echo htmlspecialchars($error_message); ?>
                <div class="mt-4">
                    <a href="manage_records.php" class="underline">Return to My Cases</a>
                </div>
            </div>
        <?php elseif ($record): ?>
            <div class="max-w-4xl mx-auto">
                <!-- Messages -->
                <?php if (!empty($message)): ?>
                    <div class="bg-green-600 text-white p-4 rounded mb-6">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <div class="form-container">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-white text-2xl font-bold">Edit Criminal Record</h2>
                        <span class="text-red-400 font-mono"><?php echo htmlspecialchars($record['case_number']); ?></span>
                    </div>
                    
                    <form method="POST" action="" enctype="multipart/form-data" class="space-y-6">
                        <!-- Suspect Information -->
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h3 class="text-white text-lg font-semibold mb-4">Suspect Information</h3>
                            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                                <!-- Mugshot Section -->
                                <div class="lg:col-span-1">
                                    <label class="text-white text-sm font-semibold mb-2 block">Suspect Mugshot</label>
                                    <div class="border-2 border-dashed border-red-600 rounded-lg p-4 text-center">
                                        <div id="mugshot-preview" class="mb-4">
                                            <?php if ($record['suspect_mugshot']): ?>
                                                <img id="mugshot-img" src="../uploads/mugshots/<?php echo htmlspecialchars($record['suspect_mugshot']); ?>" 
                                                     alt="Current Mugshot" class="w-full h-48 object-cover rounded">
                                                <div id="mugshot-placeholder" class="w-full h-48 bg-gray-600 rounded flex items-center justify-center hidden">
                                                    <span class="text-gray-400">No Image</span>
                                                </div>
                                            <?php else: ?>
                                                <img id="mugshot-img" src="" alt="Mugshot Preview" class="w-full h-48 object-cover rounded hidden">
                                                <div id="mugshot-placeholder" class="w-full h-48 bg-gray-600 rounded flex items-center justify-center">
                                                    <span class="text-gray-400">No Image</span>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <input type="file" name="suspect_mugshot" id="suspect_mugshot" 
                                               accept="image/jpeg,image/jpg,image/png" 
                                               class="hidden" onchange="previewMugshot(this)">
                                        <label for="suspect_mugshot" class="bg-red-600 text-white px-4 py-2 rounded cursor-pointer hover:bg-red-700 transition duration-300">
                                            <?php echo $record['suspect_mugshot'] ? 'Change Mugshot' : 'Upload Mugshot'; ?>
                                        </label>
                                        <p class="text-gray-400 text-xs mt-2">JPG, PNG (Max 5MB)</p>
                                    </div>
                                </div>
                                
                                <!-- Suspect Details -->
                                <div class="lg:col-span-2 space-y-4">
                                    <div>
                                        <label class="text-white text-sm font-semibold mb-2 block">Suspect Name *</label>
                                        <input type="text" name="suspect_name" class="form-control" 
                                               value="<?php echo htmlspecialchars($record['suspect_name']); ?>" required>
                                    </div>
                                    <div>
                                        <label class="text-white text-sm font-semibold mb-2 block">ID Number</label>
                                        <input type="text" name="suspect_id_number" class="form-control" 
                                               value="<?php echo htmlspecialchars($record['suspect_id_number'] ?? ''); ?>">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Crime Information -->
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h3 class="text-white text-lg font-semibold mb-4">Crime Information</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="text-white text-sm font-semibold mb-2 block">Crime Type *</label>
                                    <select name="crime_type" class="form-control" required>
                                        <option value="">Select Crime Type</option>
                                        <?php 
                                        $crime_types = ['Theft', 'Burglary', 'Assault', 'Fraud', 'Drug Offense', 'Traffic Violation', 'Domestic Violence', 'Vandalism', 'Murder', 'Robbery', 'Other'];
                                        foreach ($crime_types as $type): ?>
                                            <option value="<?php echo $type; ?>" <?php echo $record['crime_type'] == $type ? 'selected' : ''; ?>><?php echo $type; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div>
                                    <label class="text-white text-sm font-semibold mb-2 block">Crime Description *</label>
                                    <textarea name="crime_description" class="form-control" required><?php echo htmlspecialchars($record['crime_description']); ?></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Incident Details -->
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h3 class="text-white text-lg font-semibold mb-4">Incident Details</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label class="text-white text-sm font-semibold mb-2 block">Incident Date *</label>
                                    <input type="date" name="incident_date" class="form-control" 
                                           value="<?php echo htmlspecialchars($record['incident_date']); ?>" required>
                                </div>
                                <div>
                                    <label class="text-white text-sm font-semibold mb-2 block">Case Status *</label>
                                    <select name="case_status" class="form-control" required>
                                        <?php 
                                        $statuses = ['ongoing', 'solved', 'paused', 'unresolved'];
                                        foreach ($statuses as $status): ?>
                                            <option value="<?php echo $status; ?>" <?php echo $record['case_status'] == $status ? 'selected' : ''; ?>><?php echo ucfirst($status); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div>
                                    <label class="text-white text-sm font-semibold mb-2 block">Court Date</label>
                                    <input type="date" name="court_date" class="form-control" 
                                           value="<?php echo htmlspecialchars($record['court_date'] ?? ''); ?>">
                                </div>
                            </div>
                            <div class="mt-4">
                                <label class="text-white text-sm font-semibold mb-2 block">Incident Location *</label>
                                <input type="text" name="incident_location" class="form-control" 
                                       value="<?php echo htmlspecialchars($record['incident_location']); ?>" required>
                            </div>
                        </div>

                        <!-- Evidence and Witness Information -->
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h3 class="text-white text-lg font-semibold mb-4">Evidence and Witness Information</h3>
                            <div class="space-y-6">
                                <!-- Evidence Section -->
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <div>
                                        <label class="text-white text-sm font-semibold mb-2 block">Evidence Description</label>
                                        <textarea name="evidence" class="form-control"><?php echo htmlspecialchars($record['evidence'] ?? ''); ?></textarea>
                                    </div>
                                    <div>
                                        <label class="text-white text-sm font-semibold mb-2 block">Add Evidence Photos</label>
                                        <div class="border-2 border-dashed border-red-600 rounded-lg p-4">
                                            <input type="file" name="evidence_photos[]" id="evidence_photos" 
                                                   accept="image/jpeg,image/jpg,image/png,application/pdf" 
                                                   multiple class="form-control bg-transparent">
                                            <p class="text-gray-400 text-xs mt-2">Add new files: JPG, PNG, PDF (Max 5MB each)</p>
                                        </div>
                                        
                                        <!-- Existing Evidence Photos -->
                                        <?php 
                                        $existing_photos = json_decode($record['evidence_photos'] ?: '[]', true);
                                        if (!empty($existing_photos)): ?>
                                            <div class="mt-4">
                                                <p class="text-white text-sm font-semibold mb-2">Existing Evidence Photos:</p>
                                                <div class="grid grid-cols-3 gap-2">
                                                    <?php foreach ($existing_photos as $photo): ?>
                                                        <div class="relative">
                                                            <?php if (pathinfo($photo, PATHINFO_EXTENSION) === 'pdf'): ?>
                                                                <div class="w-full h-20 bg-gray-600 rounded flex items-center justify-center">
                                                                    <span class="text-gray-300 text-xs">PDF</span>
                                                                </div>
                                                            <?php else: ?>
                                                                <img src="../uploads/evidence/<?php echo htmlspecialchars($photo); ?>" 
                                                                     class="w-full h-20 object-cover rounded">
                                                            <?php endif; ?>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <!-- Witness Section -->
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <div>
                                        <label class="text-white text-sm font-semibold mb-2 block">Witness Information</label>
                                        <textarea name="witness_info" class="form-control"><?php echo htmlspecialchars($record['witness_info'] ?? ''); ?></textarea>
                                    </div>
                                    <div>
                                        <label class="text-white text-sm font-semibold mb-2 block">Witness Statements</label>
                                        <textarea name="witness_statements" class="form-control"><?php echo htmlspecialchars($record['witness_statements'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Court Information -->
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h3 class="text-white text-lg font-semibold mb-4">Court Information</h3>
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Verdict</label>
                                <textarea name="verdict" class="form-control" 
                                          placeholder="Enter court verdict if case is resolved..."><?php echo htmlspecialchars($record['verdict'] ?? ''); ?></textarea>
                            </div>
                        </div>

                        <div class="text-center pt-4">
                            <button type="submit" class="bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition duration-300 mr-4">
                                Update Record
                            </button>
                            <a href="manage_records.php" class="bg-gray-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-gray-700 transition duration-300">
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script>
        function previewMugshot(input) {
            const file = input.files[0];
            const preview = document.getElementById('mugshot-img');
            const placeholder = document.getElementById('mugshot-placeholder');
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.classList.remove('hidden');
                    placeholder.classList.add('hidden');
                };
                reader.readAsDataURL(file);
            }
        }
    </script>
</body>
</html>
