<?php
/**
 * Complete Setup Script for CRMS Enhanced Features
 * Run this file to set up all new features including tip-offs and notifications
 */

require_once 'database.php';

echo "<h1>CRMS Enhanced Features Setup</h1>";
echo "<p>Setting up all new features for the Criminal Record Management System...</p>";

try {
    $pdo = getDBConnection();
    
    echo "<h2>1. Setting up Tip-offs System...</h2>";
    
    // Create tip_offs table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS tip_offs (
            tip_id INT AUTO_INCREMENT PRIMARY KEY,
            tip_number VARCHAR(50) UNIQUE NOT NULL,
            reporter_type ENUM('anonymous', 'registered') NOT NULL DEFAULT 'anonymous',
            citizen_id INT NULL,
            tip_type VARCHAR(100) NOT NULL,
            tip_description TEXT NOT NULL,
            incident_date DATE NULL,
            incident_location VARCHAR(200) NULL,
            suspect_info TEXT NULL,
            case_visibility ENUM('public', 'private') DEFAULT 'public',
            status ENUM('new', 'under_investigation', 'resolved', 'dismissed') DEFAULT 'new',
            assigned_officer_id INT NULL,
            admin_notes TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (citizen_id) REFERENCES users(user_id) ON DELETE SET NULL,
            FOREIGN KEY (assigned_officer_id) REFERENCES users(user_id) ON DELETE SET NULL
        )
    ");
    echo "✓ Tip-offs table created<br>";
    
    // Create tip_off_updates table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS tip_off_updates (
            update_id INT AUTO_INCREMENT PRIMARY KEY,
            tip_number VARCHAR(50) NOT NULL,
            update_type ENUM('status_change', 'assignment', 'investigation_note', 'resolution') NOT NULL,
            update_description TEXT NOT NULL,
            updated_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (updated_by) REFERENCES users(user_id)
        )
    ");
    echo "✓ Tip-off updates table created<br>";
    
    echo "<h2>2. Setting up Notifications System...</h2>";
    
    // Create complaint_notifications table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS complaint_notifications (
            notification_id INT AUTO_INCREMENT PRIMARY KEY,
            complaint_number VARCHAR(50) NOT NULL,
            admin_id INT NOT NULL,
            notification_type ENUM('new_complaint', 'status_update', 'assignment') NOT NULL,
            message TEXT NOT NULL,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (admin_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    ");
    echo "✓ Complaint notifications table created<br>";
    
    // Create tip_off_notifications table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS tip_off_notifications (
            notification_id INT AUTO_INCREMENT PRIMARY KEY,
            tip_number VARCHAR(50) NOT NULL,
            admin_id INT NOT NULL,
            notification_type ENUM('new_tipoff', 'status_update', 'assignment') NOT NULL,
            message TEXT NOT NULL,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (admin_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
    ");
    echo "✓ Tip-off notifications table created<br>";
    
    echo "<h2>3. Setting up System Settings...</h2>";
    
    // Create system_settings table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS system_settings (
            setting_id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT NOT NULL,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "✓ System settings table created<br>";
    
    // Insert default settings
    $default_settings = [
        'session_timeout' => 30,
        'max_file_size' => 5,
        'password_min_length' => 6,
        'system_name' => 'Criminal Record Management System',
        'admin_email' => '<EMAIL>',
        'backup_frequency' => 'weekly'
    ];
    
    foreach ($default_settings as $key => $value) {
        $stmt = $pdo->prepare("
            INSERT INTO system_settings (setting_key, setting_value) 
            VALUES (?, ?) 
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
        ");
        $stmt->execute([$key, $value]);
    }
    echo "✓ Default system settings configured<br>";
    
    echo "<h2>4. Creating Upload Directories...</h2>";
    
    // Create upload directories if they don't exist
    $upload_dirs = [
        '../uploads',
        '../uploads/mugshots',
        '../uploads/evidence',
        '../uploads/documents'
    ];
    
    foreach ($upload_dirs as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
            echo "✓ Created directory: $dir<br>";
        } else {
            echo "✓ Directory already exists: $dir<br>";
        }
    }
    
    echo "<h2>5. Updating Database Indexes for Performance...</h2>";
    
    // Add indexes for better performance
    try {
        $pdo->exec("CREATE INDEX idx_tip_offs_status ON tip_offs(status)");
        echo "✓ Added index on tip_offs status<br>";
    } catch (PDOException $e) {
        echo "• Index on tip_offs status already exists<br>";
    }
    
    try {
        $pdo->exec("CREATE INDEX idx_tip_offs_created_at ON tip_offs(created_at)");
        echo "✓ Added index on tip_offs created_at<br>";
    } catch (PDOException $e) {
        echo "• Index on tip_offs created_at already exists<br>";
    }
    
    try {
        $pdo->exec("CREATE INDEX idx_complaints_created_at ON complaints(created_at)");
        echo "✓ Added index on complaints created_at<br>";
    } catch (PDOException $e) {
        echo "• Index on complaints created_at already exists<br>";
    }
    
    try {
        $pdo->exec("CREATE INDEX idx_criminal_records_case_status ON criminal_records(case_status)");
        echo "✓ Added index on criminal_records case_status<br>";
    } catch (PDOException $e) {
        echo "• Index on criminal_records case_status already exists<br>";
    }
    
    echo "<h2>6. Sample Data for Testing (Optional)...</h2>";
    
    // Insert sample tip-off for testing
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM tip_offs");
        $tip_count = $stmt->fetchColumn();

        if ($tip_count == 0) {
            $sample_tip_number = 'TIP-' . date('Y') . '-0001';
            $stmt = $pdo->prepare("
                INSERT INTO tip_offs (tip_number, reporter_type, tip_type, tip_description, case_visibility, status)
                VALUES (?, 'anonymous', 'Suspicious Activity', 'Sample tip-off for testing purposes', 'public', 'new')
            ");
            $stmt->execute([$sample_tip_number]);
            echo "✓ Added sample tip-off for testing<br>";

            // Add sample tip-off update (only if admin user exists)
            $admin_check = $pdo->query("SELECT user_id FROM users WHERE role = 'admin' LIMIT 1");
            $admin_user = $admin_check->fetch();

            if ($admin_user) {
                $stmt = $pdo->prepare("
                    INSERT INTO tip_off_updates (tip_number, update_type, update_description, updated_by)
                    VALUES (?, 'status_change', 'Sample tip-off created for testing', ?)
                ");
                $stmt->execute([$sample_tip_number, $admin_user['user_id']]);
                echo "✓ Added sample tip-off update<br>";
            }
        } else {
            echo "• Tip-offs already exist, skipping sample data<br>";
        }
    } catch (PDOException $e) {
        echo "• Skipping sample data creation: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>7. Verifying Setup...</h2>";
    
    // Verify all tables exist
    $tables_to_check = [
        'users', 'criminal_records', 'complaints', 'case_updates', 'hearings',
        'tip_offs', 'tip_off_updates', 'complaint_notifications', 'tip_off_notifications', 'system_settings'
    ];

    foreach ($tables_to_check as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->fetch()) {
            echo "✓ Table '$table' exists<br>";
        } else {
            echo "✗ Table '$table' missing<br>";
        }
    }
    
    echo "<h2>Setup Complete!</h2>";
    echo "<div style='background: #10b981; color: white; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>✓ All Enhanced Features Successfully Installed</h3>";
    echo "<p>The following features are now available:</p>";
    echo "<ul>";
    echo "<li>✓ Anonymous Tip-off System</li>";
    echo "<li>✓ Public/Private Case Visibility</li>";
    echo "<li>✓ Admin Notification System</li>";
    echo "<li>✓ Enhanced Analytics Dashboard</li>";
    echo "<li>✓ System Settings Management</li>";
    echo "<li>✓ Improved Checkbox Styling</li>";
    echo "<li>✓ Officer Profile Management</li>";
    echo "<li>✓ Enhanced Login/Registration Pages</li>";
    echo "<li>✓ Privacy Policy, Terms of Service, and Help Pages</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li>Test the anonymous tip-off system at: <a href='../anonymous_tipoff.php'>../anonymous_tipoff.php</a></li>";
    echo "<li>Access admin analytics at: <a href='../admin/analytics.php'>../admin/analytics.php</a></li>";
    echo "<li>Configure system settings at: <a href='../admin/system_settings.php'>../admin/system_settings.php</a></li>";
    echo "<li>Manage tip-offs at: <a href='../admin/manage_tipoffs.php'>../admin/manage_tipoffs.php</a></li>";
    echo "<li>Test officer profile management at: <a href='../officers/profile.php'>../officers/profile.php</a></li>";
    echo "</ol>";
    
    echo "<p><a href='../index.php' style='background: #dc2626; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Home Page</a></p>";
    
} catch (PDOException $e) {
    echo "<div style='background: #ef4444; color: white; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>✗ Setup Failed</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database connection and try again.</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: 'DM Sans', sans-serif;
    background: linear-gradient(135deg, #000000 0%, #121417 50%, #25282d 100%);
    color: #ffffff;
    margin: 0;
    padding: 20px;
}

h1, h2, h3 {
    color: #dc2626;
}

a {
    color: #3b82f6;
}

a:hover {
    color: #60a5fa;
}

ul, ol {
    margin-left: 20px;
}

li {
    margin-bottom: 5px;
}
</style>
