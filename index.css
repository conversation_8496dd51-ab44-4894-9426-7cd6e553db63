.logo{
    width:125px;
    height:99.5px;
     
}
body{
   font-family:"DM Sans";

}

.heading{
    font-size:80px;
    text-align:right;
    margin-right: 50px;
    margin-top:60px;
    color:#dd0000;
    /* border:2px solid white; */
}

.logo-group{
     display:flex;
     text-align: right;
}

h1{
    color:white;
     padding-top:35px
}


/* .second-div{
    background-image:url(./assets/cybersecurity4.jpg);
    margin-top:40px
} */

.slogan{
  color:#dd0000;
  font-size:20px;
  text-align:center;
  margin-right:70px;
  margin-top:100px
}

.login{
    color:#dd0000;
    font-size:30px
}

.nav-bar{
    display:flex;
    /* border:2px solid white;  */
    justify-content: space-between;
}

.bars{
   display:flex;
   flex-direction:column;
   gap: 8px;
   padding-top:60px
}

.bars:hover{
color:black;

}

.bar1{
   background-color: #dd0000;
  color:white;
   padding-top:10px;
   padding-left:10px;
   font-size: 18px;
   width:110px;
   height:45px;
     --p: 10px; /* control the shape (can be percentage) */
  aspect-ratio: 1;
  clip-path: polygon(0 0,calc(100% - var(--p)) 0,100% 100%,0 100%);
}

.bar2{
   background-color: #dd0000;
  color:white;
   padding-top:10px;
   padding-left:10px;
   font-size: 18px;
   width:95px;
   height:45px;
     --p: 10px; /* control the shape (can be percentage) */
  aspect-ratio: 1;
  clip-path: polygon(0 0,calc(100% - var(--p)) 0,100% 100%,0 100%);
}

.bar3{
   background-color: #dd0000;
   color:white;
   padding-top:10px;
   padding-left:10px;
   font-size: 18px;
   width:80px;
   height:45px;
     --p: 10px; /* control the shape (can be percentage) */
  aspect-ratio: 1;
  clip-path: polygon(0 0,calc(100% - var(--p)) 0,100% 100%,0 100%);
}

.bar4{
   background-color: #dd0000;
   color:white;
   padding-top:10px;
   padding-left:10px;
   font-size: 18px;
   width:65px;
   height:45px;
     --p: 10px; /* control the shape (can be percentage) */
  aspect-ratio: 1;
  clip-path: polygon(0 0,calc(100% - var(--p)) 0,100% 100%,0 100%);
}

.about{
    color:#dd0000;
    font-size:40px;
    margin-left:50px;
    padding-top:50px

}

.about-explanation{
color:white;
font-size:18px;
margin-left:50px;
padding-top:20px;

}

 #menuButton {
  font-size: 16px;
  padding: 10px 20px;
  cursor: pointer;
  background-color: rgba(250, 118, 23, 0.9);
  color: white;
  border: none;
  margin: 10px;
  border-radius: 5px;
  position: fixed;
  top: 10px;
  left: 10px;
  z-index: 1100;
  transition: background-color 0.3s ease;
}

#menuButton:hover {
  background-color: rgba(250, 118, 23, 1);
}

.sidebar {
  height: 100%;
  width: 0;
  position: fixed;
  z-index: 1000;                                                                                                                                                                                                                     
  top: 0;
  left: 0;
  background-color: rgba(250, 118, 23, 0.9);
  overflow-x: hidden;
  transition: width 0.4s ease;
  padding-top: 60px;
}

.sidebar.open {
  width: 250px;
  animation: bounceIn 0.5s ease; /* Add the animation here */
}

.sidebar a {
  padding: 10px 32px;
  display: block;
  text-decoration: none;
  font-size: 16px;
  color: white;
  transition: color 0.3s ease;
}

.sidebar a:hover {
  color: #000;
}

.closebtn {
  padding: 10px 32px;
  position: absolute;
  top: 10px;
  left:10px;
  font-size: 40px;
  color: white;
  text-decoration: none;
}

/* BOUNCE ANIMATION */
@keyframes bounceIn {
  0% {
    transform: translateX(100%);
  }
  60% {
    transform: translateX(-30px);
  }
  80% {
    transform: translateX(10px);
  }
  100% {
    transform: translateX(0);
  }
}

/* CHECKBOX STYLING */
input[type="checkbox"] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid #dc2626;
  border-radius: 3px;
  background-color: transparent;
  cursor: pointer;
  position: relative;
  margin-right: 8px;
  vertical-align: middle;
}

input[type="checkbox"]:checked {
  background-color: #dc2626;
  border-color: #dc2626;
}

input[type="checkbox"]:checked::before {
  content: "✓";
  position: absolute;
  top: -2px;
  left: 2px;
  font-size: 14px;
  font-weight: bold;
}

input[type="checkbox"]:hover {
  border-color: #ef4444;
  box-shadow: 0 0 5px rgba(220, 38, 38, 0.3);
}

input[type="checkbox"]:focus {
  outline: none;
  border-color: #ef4444;
  box-shadow: 0 0 8px rgba(220, 38, 38, 0.5);
}

/* CHECKBOX LABEL STYLING */
.checkbox-label {
  color: white;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.checkbox-label:hover {
  color: #dc2626;
}

option{
  color:black;
}