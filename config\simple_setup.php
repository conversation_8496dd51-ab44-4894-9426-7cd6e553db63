<?php
/**
 * Simple Setup Script for CRMS Enhanced Features
 * Run this file to set up the essential new features
 */

require_once 'database.php';

echo "<h1>CRMS Enhanced Features - Simple Setup</h1>";
echo "<p>Setting up essential new features...</p>";

try {
    $pdo = getDBConnection();
    echo "✓ Database connection successful<br><br>";
    
    echo "<h2>1. Creating Tip-offs Tables...</h2>";
    
    // Create tip_offs table
    $sql = "CREATE TABLE IF NOT EXISTS tip_offs (
        tip_id INT AUTO_INCREMENT PRIMARY KEY,
        tip_number VARCHAR(50) UNIQUE NOT NULL,
        reporter_type ENUM('anonymous', 'registered') NOT NULL DEFAULT 'anonymous',
        citizen_id INT NULL,
        tip_type VARCHAR(100) NOT NULL,
        tip_description TEXT NOT NULL,
        incident_date DATE NULL,
        incident_location VARCHAR(200) NULL,
        suspect_info TEXT NULL,
        case_visibility ENUM('public', 'private') DEFAULT 'public',
        status ENUM('new', 'under_investigation', 'resolved', 'dismissed') DEFAULT 'new',
        assigned_officer_id INT NULL,
        admin_notes TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($sql);
    echo "✓ Tip-offs table created successfully<br>";
    
    // Create tip_off_updates table
    $sql = "CREATE TABLE IF NOT EXISTS tip_off_updates (
        update_id INT AUTO_INCREMENT PRIMARY KEY,
        tip_number VARCHAR(50) NOT NULL,
        update_type ENUM('status_change', 'assignment', 'investigation_note', 'resolution') NOT NULL,
        update_description TEXT NOT NULL,
        updated_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($sql);
    echo "✓ Tip-off updates table created successfully<br>";
    
    echo "<h2>2. Creating Notification Tables...</h2>";
    
    // Create complaint_notifications table
    $sql = "CREATE TABLE IF NOT EXISTS complaint_notifications (
        notification_id INT AUTO_INCREMENT PRIMARY KEY,
        complaint_number VARCHAR(50) NOT NULL,
        admin_id INT NOT NULL,
        notification_type ENUM('new_complaint', 'status_update', 'assignment') NOT NULL,
        message TEXT NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($sql);
    echo "✓ Complaint notifications table created successfully<br>";
    
    // Create tip_off_notifications table
    $sql = "CREATE TABLE IF NOT EXISTS tip_off_notifications (
        notification_id INT AUTO_INCREMENT PRIMARY KEY,
        tip_number VARCHAR(50) NOT NULL,
        admin_id INT NOT NULL,
        notification_type ENUM('new_tipoff', 'status_update', 'assignment') NOT NULL,
        message TEXT NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($sql);
    echo "✓ Tip-off notifications table created successfully<br>";
    
    echo "<h2>3. Creating System Settings Table...</h2>";
    
    // Create system_settings table
    $sql = "CREATE TABLE IF NOT EXISTS system_settings (
        setting_id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT NOT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($sql);
    echo "✓ System settings table created successfully<br>";
    
    echo "<h2>4. Adding Default Settings...</h2>";
    
    // Insert default settings
    $default_settings = [
        ['session_timeout', '30'],
        ['max_file_size', '5'],
        ['password_min_length', '6'],
        ['system_name', 'Criminal Record Management System'],
        ['admin_email', '<EMAIL>'],
        ['backup_frequency', 'weekly']
    ];
    
    foreach ($default_settings as $setting) {
        try {
            $stmt = $pdo->prepare("INSERT IGNORE INTO system_settings (setting_key, setting_value) VALUES (?, ?)");
            $stmt->execute($setting);
            echo "✓ Added setting: {$setting[0]}<br>";
        } catch (PDOException $e) {
            echo "• Setting {$setting[0]} already exists<br>";
        }
    }
    
    echo "<h2>5. Creating Upload Directories...</h2>";
    
    // Create upload directories
    $upload_dirs = [
        '../uploads',
        '../uploads/mugshots',
        '../uploads/evidence',
        '../uploads/documents'
    ];
    
    foreach ($upload_dirs as $dir) {
        if (!file_exists($dir)) {
            if (mkdir($dir, 0755, true)) {
                echo "✓ Created directory: $dir<br>";
            } else {
                echo "✗ Failed to create directory: $dir<br>";
            }
        } else {
            echo "✓ Directory already exists: $dir<br>";
        }
    }
    
    echo "<h2>6. Testing Database Tables...</h2>";
    
    // Test each table
    $tables = ['tip_offs', 'tip_off_updates', 'complaint_notifications', 'tip_off_notifications', 'system_settings'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "✓ Table '$table' is working (contains $count records)<br>";
        } catch (PDOException $e) {
            echo "✗ Table '$table' has issues: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<div style='background: #10b981; color: white; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h2>✓ Setup Completed Successfully!</h2>";
    echo "<p>The following features are now available:</p>";
    echo "<ul>";
    echo "<li>✓ Anonymous Tip-off System</li>";
    echo "<li>✓ Admin Notification System</li>";
    echo "<li>✓ System Settings Management</li>";
    echo "<li>✓ Enhanced Database Structure</li>";
    echo "<li>✓ File Upload Directories</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>What's New:</h3>";
    echo "<ul>";
    echo "<li><strong>Anonymous Tip-offs:</strong> <a href='../anonymous_tipoff.php'>../anonymous_tipoff.php</a></li>";
    echo "<li><strong>Admin Tip-off Management:</strong> <a href='../admin/manage_tipoffs.php'>../admin/manage_tipoffs.php</a></li>";
    echo "<li><strong>Analytics Dashboard:</strong> <a href='../admin/analytics.php'>../admin/analytics.php</a></li>";
    echo "<li><strong>System Settings:</strong> <a href='../admin/system_settings.php'>../admin/system_settings.php</a></li>";
    echo "<li><strong>Officer Profiles:</strong> <a href='../officers/profile.php'>../officers/profile.php</a></li>";
    echo "<li><strong>Help & Support:</strong> <a href='../help.php'>../help.php</a></li>";
    echo "</ul>";
    
    echo "<p style='margin-top: 30px;'>";
    echo "<a href='../index.php' style='background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;'>Go to Home Page</a>";
    echo "</p>";
    
} catch (PDOException $e) {
    echo "<div style='background: #ef4444; color: white; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>✗ Setup Failed</h3>";
    echo "<p><strong>Database Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Error Code:</strong> " . $e->getCode() . "</p>";
    echo "<h4>Troubleshooting Steps:</h4>";
    echo "<ol>";
    echo "<li>Make sure XAMPP MySQL service is running</li>";
    echo "<li>Check database credentials in config/database.php</li>";
    echo "<li>Ensure the database 'crms' exists</li>";
    echo "<li>Run config/setup_database.php first if you haven't already</li>";
    echo "<li>Check MySQL error logs for more details</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<p><a href='setup_database.php' style='background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Run Basic Database Setup First</a></p>";
}
?>

<style>
body {
    font-family: 'DM Sans', sans-serif;
    background: linear-gradient(135deg, #000000 0%, #121417 50%, #25282d 100%);
    color: #ffffff;
    margin: 0;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #dc2626;
    margin-top: 30px;
}

h1 {
    text-align: center;
    font-size: 2.5em;
    margin-bottom: 10px;
}

a {
    color: #3b82f6;
    text-decoration: underline;
}

a:hover {
    color: #60a5fa;
}

ul, ol {
    margin-left: 20px;
}

li {
    margin-bottom: 8px;
}

code {
    background: rgba(55, 65, 81, 0.8);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

.success {
    color: #10b981;
    font-weight: bold;
}

.error {
    color: #ef4444;
    font-weight: bold;
}

.info {
    color: #3b82f6;
}
</style>
